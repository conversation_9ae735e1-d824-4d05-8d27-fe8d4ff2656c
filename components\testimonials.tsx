"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Star } from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Creative Director",
    content:
      "The quality and attention to detail in every piece is exceptional. LUXE has become my go-to for sophisticated wardrobe essentials.",
    rating: 5,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Fashion Consultant",
    content:
      "Impeccable craftsmanship and timeless designs. The pieces I've purchased have become staples in my wardrobe.",
    rating: 5,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Art Curator",
    content:
      "LUXE understands the art of fashion. Each piece tells a story and elevates my personal style effortlessly.",
    rating: 5,
    image: "/placeholder.svg?height=80&width=80",
  },
]

export function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-manrope text-4xl md:text-5xl font-bold mb-4">What Our Clients Say</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Discover why discerning customers choose LUXE for their fashion needs
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Card className="border-border rounded-none">
            <CardContent className="p-8 md:p-12">
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-primary text-primary" />
                  ))}
                </div>

                <blockquote className="text-xl md:text-2xl font-light leading-relaxed mb-8 text-foreground">
                  "{testimonials[currentIndex].content}"
                </blockquote>

                <div className="flex items-center justify-center space-x-4">
                  <img
                    src={testimonials[currentIndex].image || "/placeholder.svg"}
                    alt={testimonials[currentIndex].name}
                    className="w-16 h-16 object-cover"
                  />
                  <div className="text-left">
                    <p className="font-semibold text-foreground">{testimonials[currentIndex].name}</p>
                    <p className="text-muted-foreground">{testimonials[currentIndex].role}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-center items-center space-x-4 mt-8">
            <Button variant="ghost" size="icon" onClick={prevTestimonial} className="hover:bg-card">
              <ChevronLeft className="h-5 w-5" />
            </Button>

            <div className="flex space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  /* removed rounded corners from indicator dots and updated colors */
                  className={`w-2 h-2 transition-colors ${index === currentIndex ? "bg-primary" : "bg-border"}`}
                />
              ))}
            </div>

            <Button variant="ghost" size="icon" onClick={nextTestimonial} className="hover:bg-card">
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
