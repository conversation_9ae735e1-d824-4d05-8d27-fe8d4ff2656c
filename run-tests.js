// Node.js test script to verify the fixes
const fs = require('fs');
const path = require('path');

console.log('🧪 Running E-commerce App Tests...\n');

// Test 1: Verify placeholder images are replaced
console.log('📸 Test 1: Checking placeholder image replacement...');

const filesToCheck = [
  'components/hero-section.tsx',
  'components/featured-products.tsx',
  'app/product/[id]/page.tsx',
  'components/testimonials.tsx',
  'components/promotional-banner.tsx'
];

let placeholderCount = 0;
let unsplashCount = 0;

filesToCheck.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const placeholders = (content.match(/\/placeholder\.svg/g) || []).length;
    const unsplashImages = (content.match(/images\.unsplash\.com/g) || []).length;
    
    placeholderCount += placeholders;
    unsplashCount += unsplashImages;
    
    console.log(`  ${file}: ${placeholders} placeholders, ${unsplashImages} Unsplash images`);
  }
});

console.log(`\n  ✅ Total: ${placeholderCount} placeholders remaining, ${unsplashCount} Unsplash images added`);
console.log(`  ${placeholderCount === 0 ? '✅ PASS' : '❌ FAIL'}: All placeholder images replaced\n`);

// Test 2: Verify price formatting functionality
console.log('💰 Test 2: Checking price formatting implementation...');

const headerFile = path.join(__dirname, 'components/header.tsx');
const featuredProductsFile = path.join(__dirname, 'components/featured-products.tsx');
const productPageFile = path.join(__dirname, 'app/product/[id]/page.tsx');

let headerHasLocalStorage = false;
let featuredProductsHasListener = false;
let productPageHasFormatPrice = false;

if (fs.existsSync(headerFile)) {
  const content = fs.readFileSync(headerFile, 'utf8');
  headerHasLocalStorage = content.includes('localStorage.setItem') && content.includes('handleCountryChange');
  console.log(`  Header localStorage sync: ${headerHasLocalStorage ? '✅ PASS' : '❌ FAIL'}`);
}

if (fs.existsSync(featuredProductsFile)) {
  const content = fs.readFileSync(featuredProductsFile, 'utf8');
  featuredProductsHasListener = content.includes('addEventListener("storage"') && content.includes('formatPrice');
  console.log(`  Featured Products storage listener: ${featuredProductsHasListener ? '✅ PASS' : '❌ FAIL'}`);
}

if (fs.existsSync(productPageFile)) {
  const content = fs.readFileSync(productPageFile, 'utf8');
  productPageHasFormatPrice = content.includes('formatPrice') && content.includes('currencyRates');
  console.log(`  Product Page price formatting: ${productPageHasFormatPrice ? '✅ PASS' : '❌ FAIL'}`);
}

// Test 3: Verify currency rates and countries data
console.log('\n🌍 Test 3: Checking currency configuration...');

const expectedCurrencies = ['USD', 'ZAR', 'BWP', 'AUD'];
const expectedCountries = ['US', 'ZA', 'BW', 'AU'];

let currencyConfigCorrect = false;
if (fs.existsSync(featuredProductsFile)) {
  const content = fs.readFileSync(featuredProductsFile, 'utf8');
  currencyConfigCorrect = expectedCurrencies.every(currency => content.includes(currency)) &&
                         expectedCountries.every(country => content.includes(`"${country}"`));
}

console.log(`  Currency configuration: ${currencyConfigCorrect ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 Test Summary:');
const allTestsPassed = placeholderCount === 0 && 
                      headerHasLocalStorage && 
                      featuredProductsHasListener && 
                      productPageHasFormatPrice && 
                      currencyConfigCorrect;

console.log(`  Images replaced: ${placeholderCount === 0 ? '✅' : '❌'}`);
console.log(`  Price formatting fixed: ${headerHasLocalStorage && featuredProductsHasListener && productPageHasFormatPrice ? '✅' : '❌'}`);
console.log(`  Currency config: ${currencyConfigCorrect ? '✅' : '❌'}`);
console.log(`\n🎉 Overall result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

if (allTestsPassed) {
  console.log('\n🚀 Ready for manual testing:');
  console.log('   1. Open http://localhost:3000');
  console.log('   2. Verify images are loaded');
  console.log('   3. Test country selection and price updates');
  console.log('   4. Navigate to product pages and verify formatting');
}
