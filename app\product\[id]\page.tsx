"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, ShoppingBag, Star, Minus, Plus, ArrowLeft } from "lucide-react"

// Currency rates and countries data
const currencyRates = {
  USD: { rate: 1, symbol: "$" },
  ZAR: { rate: 18.5, symbol: "R" },
  BWP: { rate: 13.2, symbol: "P" },
  AUD: { rate: 1.5, symbol: "A$" },
}

const countries = [
  { code: "US", currency: "USD" },
  { code: "ZA", currency: "ZAR" },
  { code: "BW", currency: "BWP" },
  { code: "AU", currency: "AUD" },
]

// Mock product data
const product = {
  id: 1,
  name: "Silk Midi Dress",
  basePrice: 485,
  originalBasePrice: 620,
  description:
    "Crafted from the finest silk, this midi dress embodies timeless elegance with its clean lines and sophisticated silhouette. Perfect for both professional settings and evening occasions.",
  features: ["100% Pure Silk", "Dry Clean Only", "Made in Italy", "Lined Interior", "Hidden Back Zipper"],
  sizes: ["XS", "S", "M", "L", "XL"],
  colors: ["Black", "White", "Navy"],
  images: [
    "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
    "https://images.unsplash.com/photo-1583496661160-fb5886a13d27?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
    "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
    "https://images.unsplash.com/photo-1571945153237-4929e783af4a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
  ],
  rating: 4.8,
  reviews: 124,
  inStock: true,
}

export default function ProductPage() {
  const params = useParams()
  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedSize, setSelectedSize] = useState("")
  const [selectedColor, setSelectedColor] = useState("Black")
  const [quantity, setQuantity] = useState(1)
  const [selectedCountry, setSelectedCountry] = useState("US")

  // Load country from localStorage and listen for changes
  useEffect(() => {
    const handleStorageChange = () => {
      const country = localStorage.getItem("selectedCountry") || "US"
      setSelectedCountry(country)
    }

    // Load initial value from localStorage
    handleStorageChange()

    // Listen for storage changes from other components
    window.addEventListener("storage", handleStorageChange)

    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])

  // Format price based on selected country
  const formatPrice = (basePrice: number) => {
    const country = countries.find((c) => c.code === selectedCountry)
    const currency = country?.currency || "USD"
    const { rate, symbol } = currencyRates[currency as keyof typeof currencyRates]
    const convertedPrice = Math.round(basePrice * rate)
    return `${symbol}${convertedPrice}`
  }

  const handleAddToCart = () => {
    if (!selectedSize) {
      alert("Please select a size")
      return
    }
    // Add to cart logic here
    console.log("Added to cart:", {
      product: {
        ...product,
        formattedPrice: formatPrice(product.basePrice)
      },
      selectedSize,
      selectedColor,
      quantity
    })
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-primary">
            Home
          </Link>
          <span>/</span>
          <Link href="/women" className="hover:text-primary">
            Women
          </Link>
          <span>/</span>
          <span className="text-foreground">{product.name}</span>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-[3/4] overflow-hidden bg-card">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>

            <div className="grid grid-cols-4 gap-4">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  /* removed rounded corners from thumbnail images and updated border colors */
                  className={`aspect-square overflow-hidden border-2 transition-colors ${
                    selectedImage === index ? "border-primary" : "border-border"
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} view ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="font-manrope text-3xl md:text-4xl font-bold mb-2">{product.name}</h1>
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(product.rating) ? "fill-primary text-primary" : "text-muted-foreground"
                      }`}
                    />
                  ))}
                  <span className="text-sm text-muted-foreground ml-2">
                    {product.rating} ({product.reviews} reviews)
                  </span>
                </div>
              </div>

              <div className="flex items-center space-x-4 mb-6">
                <span className="text-3xl font-bold">{formatPrice(product.basePrice)}</span>
                {product.originalBasePrice && (
                  <span className="text-xl text-muted-foreground line-through">{formatPrice(product.originalBasePrice)}</span>
                )}
                {product.originalBasePrice && (
                  /* updated badge colors to use pure black/white */
                  <Badge variant="secondary" className="bg-primary text-primary-foreground rounded-none">
                    Save {formatPrice(product.originalBasePrice - product.basePrice)}
                  </Badge>
                )}
              </div>
            </div>

            <p className="text-muted-foreground leading-relaxed">{product.description}</p>

            {/* Color Selection */}
            <div>
              <h3 className="font-semibold mb-3">Color: {selectedColor}</h3>
              <div className="flex space-x-3">
                {product.colors.map((color) => (
                  <button
                    key={color}
                    onClick={() => setSelectedColor(color)}
                    /* removed rounded corners from color swatches and updated border colors */
                    className={`w-12 h-12 border-2 transition-colors ${
                      selectedColor === color ? "border-primary" : "border-border"
                    } ${color === "Black" ? "bg-black" : color === "White" ? "bg-white" : "bg-blue-900"}`}
                  >
                    <span className="sr-only">{color}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Size Selection */}
            <div>
              <h3 className="font-semibold mb-3">Size</h3>
              <div className="grid grid-cols-5 gap-3">
                {product.sizes.map((size) => (
                  <button
                    key={size}
                    onClick={() => setSelectedSize(size)}
                    /* removed rounded corners from size buttons and updated colors */
                    className={`py-3 px-4 border text-center transition-colors ${
                      selectedSize === size
                        ? "border-primary bg-primary text-primary-foreground"
                        : "border-border hover:border-primary"
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
              <Link href="/size-guide" className="text-sm text-primary hover:underline mt-2 inline-block">
                Size Guide
              </Link>
            </div>

            {/* Quantity */}
            <div>
              <h3 className="font-semibold mb-3">Quantity</h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center border border-border">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 hover:bg-card transition-colors"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-4 py-2 min-w-[3rem] text-center">{quantity}</span>
                  <button onClick={() => setQuantity(quantity + 1)} className="p-2 hover:bg-card transition-colors">
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <Button
                onClick={handleAddToCart}
                size="lg"
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground rounded-none"
                disabled={!product.inStock}
              >
                <ShoppingBag className="h-5 w-5 mr-2" />
                {product.inStock ? "Add to Cart" : "Out of Stock"}
              </Button>

              <Button variant="outline" size="lg" className="w-full bg-transparent rounded-none">
                <Heart className="h-5 w-5 mr-2" />
                Add to Wishlist
              </Button>
            </div>

            {/* Features */}
            <Card className="rounded-none">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4">Product Features</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="text-muted-foreground flex items-center">
                      <span className="w-2 h-2 bg-primary mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Back to Products */}
        <div className="mt-12">
          <Link href="/">
            <Button variant="ghost" className="hover:bg-card">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
        </div>
      </main>

      <Footer />
    </div>
  )
}
