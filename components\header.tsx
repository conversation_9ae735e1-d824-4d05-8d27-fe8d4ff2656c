"use client"

import { useState } from "react"
import Link from "next/link"
import { Search, ShoppingBag, Menu, X, Globe } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const countries = [
  { code: "US", name: "USA", currency: "USD", symbol: "$" },
  { code: "ZA", name: "South Africa", currency: "ZAR", symbol: "R" },
  { code: "BW", name: "Botswana", currency: "BWP", symbol: "P" },
  { code: "AU", name: "Australia", currency: "AUD", symbol: "A$" },
]

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [selectedCountry, setSelectedCountry] = useState("US")

  const currentCountry = countries.find((c) => c.code === selectedCountry)

  return (
    <header className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="font-manrope text-2xl font-bold tracking-tight">
            LUXE
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/collections" className="text-sm font-medium hover:text-primary transition-colors">
              Collections
            </Link>
            <Link href="/women" className="text-sm font-medium hover:text-primary transition-colors">
              Women
            </Link>
            <Link href="/men" className="text-sm font-medium hover:text-primary transition-colors">
              Men
            </Link>
            <Link href="/accessories" className="text-sm font-medium hover:text-primary transition-colors">
              Accessories
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-primary transition-colors">
              About
            </Link>
          </nav>

          {/* Search and Actions */}
          <div className="flex items-center space-x-4">
            <Select value={selectedCountry} onValueChange={setSelectedCountry}>
              <SelectTrigger className="w-32 border-border rounded-none bg-transparent">
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4" />
                  <SelectValue />
                </div>
              </SelectTrigger>
              <SelectContent className="rounded-none border-border">
                {countries.map((country) => (
                  <SelectItem key={country.code} value={country.code} className="rounded-none">
                    {country.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="hidden sm:flex items-center">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search..."
                  className="pl-10 w-64 bg-card border-border rounded-none"
                />
              </div>
            </div>

            <Link href="/cart">
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingBag className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-primary text-primary-foreground text-xs flex items-center justify-center">
                  2
                </span>
              </Button>
            </Link>

            {/* Mobile menu button */}
            <Button variant="ghost" size="icon" className="md:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-border">
            <nav className="py-4 space-y-4">
              <div className="pb-4 border-b border-border">
                <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                  <SelectTrigger className="w-full border-border rounded-none bg-transparent">
                    <div className="flex items-center space-x-2">
                      <Globe className="h-4 w-4" />
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent className="rounded-none border-border">
                    {countries.map((country) => (
                      <SelectItem key={country.code} value={country.code} className="rounded-none">
                        {country.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Link href="/collections" className="block text-sm font-medium hover:text-primary transition-colors">
                Collections
              </Link>
              <Link href="/women" className="block text-sm font-medium hover:text-primary transition-colors">
                Women
              </Link>
              <Link href="/men" className="block text-sm font-medium hover:text-primary transition-colors">
                Men
              </Link>
              <Link href="/accessories" className="block text-sm font-medium hover:text-primary transition-colors">
                Accessories
              </Link>
              <Link href="/about" className="block text-sm font-medium hover:text-primary transition-colors">
                About
              </Link>
              <div className="pt-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Search..." className="pl-10 bg-card border-border rounded-none" />
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
