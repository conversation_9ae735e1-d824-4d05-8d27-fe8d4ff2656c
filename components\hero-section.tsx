import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80" alt="Fashion Hero" className="w-full h-full object-cover" />
        <div className="absolute inset-0 bg-black/30" />
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
        <h1 className="font-manrope text-5xl md:text-7xl font-bold mb-6 tracking-tight">
          Timeless
          <span className="block">Elegance</span>
        </h1>
        <p className="text-xl md:text-2xl mb-8 font-light leading-relaxed max-w-2xl mx-auto">
          Discover our curated collection of sophisticated fashion pieces designed for the modern connoisseur
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-white hover:bg-white/90 text-black font-medium px-8 py-3 rounded-none">
            Shop Collection
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-white text-white hover:bg-white hover:text-black px-8 py-3 bg-transparent rounded-none"
          >
            View Lookbook
          </Button>
        </div>
      </div>
    </section>
  )
}
