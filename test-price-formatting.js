// Simple test to verify price formatting functionality
// This can be run in the browser console to test the formatPrice function

const currencyRates = {
  USD: { rate: 1, symbol: "$" },
  ZAR: { rate: 18.5, symbol: "R" },
  BWP: { rate: 13.2, symbol: "P" },
  AUD: { rate: 1.5, symbol: "A$" },
}

const countries = [
  { code: "US", currency: "USD" },
  { code: "ZA", currency: "ZAR" },
  { code: "BW", currency: "BWP" },
  { code: "AU", currency: "AUD" },
]

function formatPrice(basePrice, selectedCountry) {
  const country = countries.find((c) => c.code === selectedCountry)
  const currency = country?.currency || "USD"
  const { rate, symbol } = currencyRates[currency]
  const convertedPrice = Math.round(basePrice * rate)
  return `${symbol}${convertedPrice}`
}

// Test cases
console.log("Testing price formatting:")
console.log("Base price: $485")
console.log("US (USD):", formatPrice(485, "US")) // Should be $485
console.log("South Africa (ZAR):", formatPrice(485, "ZA")) // Should be R8973
console.log("Botswana (BWP):", formatPrice(485, "BW")) // Should be P6402
console.log("Australia (AUD):", formatPrice(485, "AU")) // Should be A$728

// Test localStorage functionality
console.log("\nTesting localStorage functionality:")
localStorage.setItem("selectedCountry", "ZA")
console.log("Set country to ZA, localStorage value:", localStorage.getItem("selectedCountry"))

// Simulate storage event
window.dispatchEvent(new StorageEvent("storage", {
  key: "selectedCountry",
  newValue: "AU",
  storageArea: localStorage
}))
console.log("Dispatched storage event for AU")

console.log("\nTest completed. Check the browser UI to verify:")
console.log("1. All placeholder images are replaced with Unsplash images")
console.log("2. Changing country in header updates prices in featured products")
console.log("3. Product page shows formatted prices based on selected country")
