@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated to pure black and white only - no greys or other colors */
  --background: oklch(1 0 0); /* Pure white */
  --foreground: oklch(0 0 0); /* Pure black */
  --card: oklch(1 0 0); /* Pure white for cards */
  --card-foreground: oklch(0 0 0); /* Black text on cards */
  --popover: oklch(1 0 0); /* White popover */
  --popover-foreground: oklch(0 0 0); /* Black text on popover */
  --primary: oklch(0 0 0); /* Black primary */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(1 0 0); /* White secondary */
  --secondary-foreground: oklch(0 0 0); /* Black text on secondary */
  --muted: oklch(1 0 0); /* White for muted elements */
  --muted-foreground: oklch(0 0 0); /* Black for muted text */
  --accent: oklch(0 0 0); /* Black accent */
  --accent-foreground: oklch(1 0 0); /* White text on accent */
  --destructive: oklch(0 0 0);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0 0 0); /* Black borders */
  --input: oklch(1 0 0); /* White input background */
  --ring: oklch(0 0 0 / 0.2); /* Black ring with opacity */
  --chart-1: oklch(0 0 0);
  --chart-2: oklch(1 0 0);
  --chart-3: oklch(0 0 0);
  --chart-4: oklch(1 0 0);
  --chart-5: oklch(0 0 0);
  --radius: 0rem; /* Removed all rounded corners */
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(1 0 0);
  --sidebar-primary-foreground: oklch(0 0 0);
  --sidebar-accent: oklch(0 0 0);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0 0 0);
  --sidebar-ring: oklch(0 0 0 / 0.1);
}

.dark {
  /* Dark mode using pure black and white only */
  --background: oklch(0 0 0); /* Pure black background */
  --foreground: oklch(1 0 0); /* Pure white text */
  --card: oklch(0 0 0); /* Black card background */
  --card-foreground: oklch(1 0 0); /* White text on cards */
  --popover: oklch(0 0 0); /* Black popover */
  --popover-foreground: oklch(1 0 0); /* White text on popover */
  --primary: oklch(1 0 0); /* White primary */
  --primary-foreground: oklch(0 0 0); /* Black text on primary */
  --secondary: oklch(0 0 0); /* Black secondary */
  --secondary-foreground: oklch(1 0 0); /* White text on secondary */
  --muted: oklch(0 0 0); /* Black muted */
  --muted-foreground: oklch(1 0 0); /* White for muted text */
  --accent: oklch(1 0 0); /* White accent */
  --accent-foreground: oklch(0 0 0); /* Black text on accent */
  --destructive: oklch(1 0 0);
  --destructive-foreground: oklch(0 0 0);
  --border: oklch(1 0 0); /* White borders */
  --input: oklch(0 0 0); /* Black input background */
  --ring: oklch(1 0 0 / 0.2); /* White ring with opacity */
  --chart-1: oklch(1 0 0);
  --chart-2: oklch(0 0 0);
  --chart-3: oklch(1 0 0);
  --chart-4: oklch(0 0 0);
  --chart-5: oklch(1 0 0);
  --sidebar: oklch(0 0 0);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(1 0 0);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(1 0 0);
  --sidebar-ring: oklch(1 0 0 / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: var(--font-geist);
  --font-serif: var(--font-manrope);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}
