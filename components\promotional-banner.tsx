import { Button } from "@/components/ui/button"

export function PromotionalBanner() {
  return (
    <section className="py-20 bg-card">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1">
            <h2 className="font-manrope text-4xl md:text-5xl font-bold mb-6">
              Exclusive
              <span className="block text-primary">Spring Collection</span>
            </h2>
            <p className="text-muted-foreground text-lg mb-8 leading-relaxed">
              Embrace the season with our carefully crafted pieces that blend contemporary design with timeless
              elegance. Limited quantities available.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-primary hover:bg-primary/90 px-8 rounded-none">
                Shop Now
              </Button>
              <Button variant="outline" size="lg" className="px-8 bg-transparent rounded-none">
                Learn More
              </Button>
            </div>
            <div className="mt-8 flex items-center space-x-8 text-sm text-muted-foreground">
              <div>
                <span className="block font-semibold text-foreground">Free Shipping</span>
                <span>On orders over $200</span>
              </div>
              <div>
                <span className="block font-semibold text-foreground">30-Day Returns</span>
                <span>Hassle-free returns</span>
              </div>
            </div>
          </div>

          <div className="order-1 lg:order-2">
            <img src="https://images.unsplash.com/photo-1483985988355-763728e1935b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Spring Collection" className="w-full h-auto" />
          </div>
        </div>
      </div>
    </section>
  )
}
