"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Heart, ShoppingBag } from "lucide-react"

const products = [
  {
    id: 1,
    name: "Oversized Blazer",
    basePrice: 485,
    image: "/placeholder.svg?height=600&width=400",
    category: "Outerwear",
  },
  {
    id: 2,
    name: "Pleated Midi Skirt",
    basePrice: 295,
    image: "/placeholder.svg?height=600&width=400",
    category: "Bottoms",
  },
  {
    id: 3,
    name: "Silk Camisole",
    basePrice: 180,
    image: "/placeholder.svg?height=600&width=400",
    category: "Tops",
  },
  {
    id: 4,
    name: "Wide-Leg Trousers",
    basePrice: 340,
    image: "/placeholder.svg?height=600&width=400",
    category: "Bottoms",
  },
  {
    id: 5,
    name: "Cropped Cardigan",
    basePrice: 225,
    image: "/placeholder.svg?height=600&width=400",
    category: "Knitwear",
  },
  {
    id: 6,
    name: "Statement Earrings",
    basePrice: 125,
    image: "/placeholder.svg?height=600&width=400",
    category: "Jewelry",
  },
]

const currencyRates = {
  USD: { rate: 1, symbol: "$" },
  ZAR: { rate: 18.5, symbol: "R" },
  BWP: { rate: 13.2, symbol: "P" },
  AUD: { rate: 1.5, symbol: "A$" },
}

const countries = [
  { code: "US", currency: "USD" },
  { code: "ZA", currency: "ZAR" },
  { code: "BW", currency: "BWP" },
  { code: "AU", currency: "AUD" },
]

export function FeaturedProducts() {
  const [selectedCountry, setSelectedCountry] = useState("US")

  useEffect(() => {
    const handleStorageChange = () => {
      const country = localStorage.getItem("selectedCountry") || "US"
      setSelectedCountry(country)
    }

    window.addEventListener("storage", handleStorageChange)
    handleStorageChange()

    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])

  const formatPrice = (basePrice: number) => {
    const country = countries.find((c) => c.code === selectedCountry)
    const currency = country?.currency || "USD"
    const { rate, symbol } = currencyRates[currency as keyof typeof currencyRates]
    const convertedPrice = Math.round(basePrice * rate)
    return `${symbol}${convertedPrice}`
  }

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-manrope text-4xl md:text-5xl font-bold mb-4">Featured Collection</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Carefully curated pieces that embody sophistication and timeless style
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer border-0 hover:shadow-lg transition-all duration-300 rounded-none bg-transparent"
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  <img
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    className="w-full h-96 object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button variant="ghost" size="icon" className="bg-white/90 hover:bg-white text-black rounded-none">
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                </div>

                <div className="p-6">
                  <p className="text-sm text-muted-foreground mb-2">{product.category}</p>
                  <h3 className="font-manrope text-xl font-semibold mb-2">{product.name}</h3>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-medium">{formatPrice(product.basePrice)}</span>
                    <Link href={`/product/${product.id}`}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-none"
                      >
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        Add to Cart
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg" className="px-8 bg-transparent rounded-none">
            View All Products
          </Button>
        </div>
      </div>
    </section>
  )
}
